using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.EditorInput;
using CADFileSaver.Core;
using AcadApp = Autodesk.AutoCAD.ApplicationServices.Application;

namespace CADFileSaver.Features.ColorSeparation
{
    /// <summary>
    /// 横向分色处理器
    /// 负责处理横向分色功能的完整流程，与其他Box功能完全独立
    /// </summary>
    public class HorizontalColorSeparationProcessor
    {
        #region 私有字段

        private readonly ColorSeparationConfig _config;
        private readonly List<ColorCombination> _colorCombinations;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="config">分色配置</param>
        public HorizontalColorSeparationProcessor(ColorSeparationConfig config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _colorCombinations = GenerateColorCombinations(config);
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 执行横向分色操作
        /// </summary>
        /// <param name="selectionSet">用户选择的图形集合</param>
        /// <returns>分色操作结果</returns>
        public SeparationResult ExecuteHorizontalColorSeparation(SelectionSet selectionSet)
        {
            try
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                var db = doc.Database;
                var editor = doc.Editor;

                // 验证输入参数
                if (selectionSet == null || selectionSet.Count == 0)
                {
                    throw new ColorSeparationException(
                        ColorSeparationException.ErrorType.NoEntitiesSelected,
                        "未选择任何图形实体");
                }

                editor.WriteMessage($"\n开始横向分色处理，共 {_colorCombinations.Count} 个颜色组合...\n");

                using (var transaction = db.TransactionManager.StartTransaction())
                {
                    // 获取所有选择的实体
                    var allEntities = GetEntitiesFromSelection(selectionSet, transaction);
                    
                    if (!allEntities.Any())
                    {
                        throw new ColorSeparationException(
                            ColorSeparationException.ErrorType.NoEntitiesSelected,
                            "选择集中没有有效的图形实体");
                    }

                    // 计算单位长度
                    var unitLength = GraphicsOperationHelper.CalculateUnitLength(allEntities);
                    editor.WriteMessage($"计算得到单位长度: {unitLength:F2}\n");

                    // 获取模型空间
                    var modelSpace = GetModelSpace(db, transaction);

                    int totalProcessed = 0;
                    int totalCopied = 0;

                    // 处理每个颜色组合
                    for (int i = 0; i < _colorCombinations.Count; i++)
                    {
                        var combination = _colorCombinations[i];
                        var offsetX = unitLength * i; // 第i个组合的X轴偏移

                        editor.WriteMessage($"处理第 {i + 1} 个组合: {combination.GetDescription()}, 偏移距离: {offsetX:F2}\n");

                        // 筛选匹配当前颜色组合的实体
                        var matchingEntities = GraphicsOperationHelper.FilterEntitiesByColor(
                            allEntities, combination.FrameColor, combination.SeparationColor);

                        if (matchingEntities.Any())
                        {
                            // 复制并移动实体
                            var copiedCount = CopyAndMoveEntities(matchingEntities, offsetX, modelSpace, transaction);
                            totalCopied += copiedCount;
                            totalProcessed++;

                            editor.WriteMessage($"  -> 复制了 {copiedCount} 个图形实体\n");
                        }
                        else
                        {
                            editor.WriteMessage($"  -> 未找到匹配的图形实体\n");
                        }
                    }

                    // 提交事务
                    transaction.Commit();

                    editor.WriteMessage($"\n横向分色完成！处理了 {totalProcessed} 个颜色组合，共复制 {totalCopied} 个图形实体\n");

                    return SeparationResult.CreateSuccess(totalProcessed, totalCopied);
                }
            }
            catch (ColorSeparationException)
            {
                throw; // 重新抛出分色异常
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.GraphicsOperationFailed,
                    $"执行横向分色时发生未预期的错误: {ex.Message}", ex);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 从选择集获取实体列表
        /// </summary>
        private List<Entity> GetEntitiesFromSelection(SelectionSet selectionSet, Transaction transaction)
        {
            var entities = new List<Entity>();

            foreach (SelectedObject selectedObj in selectionSet)
            {
                if (selectedObj != null)
                {
                    var entity = transaction.GetObject(selectedObj.ObjectId, OpenMode.ForRead) as Entity;
                    if (entity != null && !entity.IsDisposed)
                    {
                        entities.Add(entity);
                    }
                }
            }

            return entities;
        }

        /// <summary>
        /// 获取模型空间块表记录
        /// </summary>
        private BlockTableRecord GetModelSpace(Database db, Transaction transaction)
        {
            var blockTable = (BlockTable)transaction.GetObject(db.BlockTableId, OpenMode.ForRead);
            return (BlockTableRecord)transaction.GetObject(blockTable[BlockTableRecord.ModelSpace], OpenMode.ForWrite);
        }

        /// <summary>
        /// 复制并移动实体到指定位置
        /// </summary>
        private int CopyAndMoveEntities(List<Entity> entities, double offsetX, BlockTableRecord modelSpace, Transaction transaction)
        {
            int copiedCount = 0;

            foreach (var entity in entities)
            {
                try
                {
                    // 克隆实体
                    var clonedEntity = (Entity)entity.Clone();
                    
                    // 应用X轴偏移变换
                    var displacement = new Vector3d(offsetX, 0, 0);
                    var transformMatrix = Matrix3d.Displacement(displacement);
                    clonedEntity.TransformBy(transformMatrix);

                    // 添加到模型空间
                    modelSpace.AppendEntity(clonedEntity);
                    transaction.AddNewlyCreatedDBObject(clonedEntity, true);

                    copiedCount++;
                }
                catch (System.Exception ex)
                {
                    // 记录单个实体复制失败，但继续处理其他实体
                    var doc = AcadApp.DocumentManager.MdiActiveDocument;
                    doc?.Editor.WriteMessage($"    警告: 复制实体失败 - {ex.Message}\n");
                }
            }

            return copiedCount;
        }

        /// <summary>
        /// 生成颜色组合列表
        /// </summary>
        private List<ColorCombination> GenerateColorCombinations(ColorSeparationConfig config)
        {
            var combinations = new List<ColorCombination>();

            try
            {
                for (int i = 0; i < config.SeparationColors.Count; i++)
                {
                    var combination = new ColorCombination
                    {
                        FrameColor = config.FrameColor,
                        SeparationColor = config.SeparationColors[i],
                        FrameColorIndex = GetColorIndex(config.FrameColor),
                        SeparationColorIndex = GetColorIndex(config.SeparationColors[i]),
                        Order = i + 1
                    };
                    combinations.Add(combination);
                }

                return combinations;
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.InvalidColorSelection,
                    $"生成颜色组合时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取颜色索引
        /// </summary>
        private short GetColorIndex(string colorName)
        {
            var colorMapping = new Dictionary<string, short>
            {
                { "红色", 1 }, { "黄色", 2 }, { "绿色", 3 },
                { "青色", 4 }, { "蓝色", 5 }, { "洋红", 6 }
            };

            return colorMapping.ContainsKey(colorName) ? colorMapping[colorName] : (short)7;
        }

        #endregion
    }
}
