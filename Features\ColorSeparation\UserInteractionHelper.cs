using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.DatabaseServices;

namespace CADFileSaver.Features.ColorSeparation
{
    /// <summary>
    /// 用户交互辅助类
    /// 处理分色功能中的用户交互和反馈
    /// </summary>
    public static class UserInteractionHelper
    {
        /// <summary>
        /// 获取用户选择的图形
        /// </summary>
        /// <param name="editor">CAD编辑器对象</param>
        /// <param name="status">返回的选择状态</param>
        /// <returns>用户选择的图形集合</returns>
        public static SelectionSet GetUserSelection(Editor editor, out PromptStatus status)
        {
            try
            {
                // 设置选择选项
                var selectionOptions = new PromptSelectionOptions
                {
                    MessageForAdding = "选择要进行竖向分色的图形: ",
                    MessageForRemoval = "移除图形: ",
                    AllowDuplicates = false,
                    RejectObjectsFromNonCurrentSpace = true
                };

                // 获取用户选择
                var selectionResult = editor.GetSelection(selectionOptions);
                status = selectionResult.Status;

                // 检查选择结果
                if (selectionResult.Status == PromptStatus.OK)
                {
                    return selectionResult.Value;
                }
                else if (selectionResult.Status == PromptStatus.Cancel)
                {
                    editor.WriteMessage("\n用户取消选择\n");
                    return null;
                }
                else
                {
                    editor.WriteMessage("\n未选择任何图形\n");
                    return null;
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n获取用户选择时发生错误: {ex.Message}\n");
                status = PromptStatus.Error;
                return null;
            }
        }

        /// <summary>
        /// 显示操作开始信息
        /// </summary>
        /// <param name="editor">CAD编辑器对象</param>
        /// <param name="config">分色配置</param>
        public static void ShowOperationStart(Editor editor, ColorSeparationConfig config)
        {
            if (editor == null) return;

            editor.WriteMessage("\n" + new string('=', 50) + "\n");
            editor.WriteMessage("开始执行竖向分色操作\n");
            editor.WriteMessage($"配置信息: {config.GetDescription()}\n");
            editor.WriteMessage(new string('=', 50) + "\n");
        }

        /// <summary>
        /// 显示进度信息
        /// </summary>
        /// <param name="editor">CAD编辑器对象</param>
        /// <param name="message">进度消息</param>
        /// <param name="current">当前进度</param>
        /// <param name="total">总数</param>
        public static void ShowProgress(Editor editor, string message, int current, int total)
        {
            if (editor == null) return;

            var percentage = total > 0 ? (current * 100 / total) : 0;
            editor.WriteMessage($"[{current}/{total}] ({percentage}%) {message}\n");
        }

        /// <summary>
        /// 显示颜色组合处理信息
        /// </summary>
        /// <param name="editor">CAD编辑器对象</param>
        /// <param name="combination">颜色组合</param>
        /// <param name="entityCount">匹配的实体数量</param>
        /// <param name="yOffset">Y轴偏移量</param>
        public static void ShowCombinationProcessing(Editor editor, ColorCombination combination, 
            int entityCount, double yOffset)
        {
            if (editor == null) return;

            editor.WriteMessage($"处理 {combination.GetDescription()}: ");
            editor.WriteMessage($"找到 {entityCount} 个匹配图形, ");
            editor.WriteMessage($"Y偏移 {yOffset:F1}\n");
        }

        /// <summary>
        /// 显示操作结果
        /// </summary>
        /// <param name="editor">CAD编辑器对象</param>
        /// <param name="result">分色结果</param>
        public static void ShowResult(Editor editor, SeparationResult result)
        {
            if (editor == null) return;

            editor.WriteMessage("\n" + new string('-', 50) + "\n");
            
            if (result.Success)
            {
                editor.WriteMessage("✓ 竖向分色操作完成\n");
                editor.WriteMessage($"处理结果: {result.Message}\n");
                
                if (result.ProcessedCombinations > 0)
                {
                    editor.WriteMessage($"成功处理: {result.ProcessedCombinations} 个颜色组合\n");
                    editor.WriteMessage($"涉及图形: {result.TotalEntities} 个实体\n");
                }
            }
            else
            {
                editor.WriteMessage("✗ 竖向分色操作失败\n");
                editor.WriteMessage($"失败原因: {result.Message}\n");
                
                if (!string.IsNullOrEmpty(result.ErrorMessage))
                {
                    editor.WriteMessage($"详细错误: {result.ErrorMessage}\n");
                }
            }
            
            editor.WriteMessage(new string('-', 50) + "\n");
        }

        /// <summary>
        /// 显示警告信息
        /// </summary>
        /// <param name="editor">CAD编辑器对象</param>
        /// <param name="message">警告消息</param>
        public static void ShowWarning(Editor editor, string message)
        {
            if (editor == null) return;
            editor.WriteMessage($"⚠ 警告: {message}\n");
        }

        /// <summary>
        /// 显示错误信息
        /// </summary>
        /// <param name="editor">CAD编辑器对象</param>
        /// <param name="message">错误消息</param>
        public static void ShowError(Editor editor, string message)
        {
            if (editor == null) return;
            editor.WriteMessage($"✗ 错误: {message}\n");
        }

        /// <summary>
        /// 显示调试信息（仅在调试模式下显示）
        /// </summary>
        /// <param name="editor">CAD编辑器对象</param>
        /// <param name="message">调试消息</param>
        public static void ShowDebugInfo(Editor editor, string message)
        {
#if DEBUG
            if (editor == null) return;
            editor.WriteMessage($"[DEBUG] {message}\n");
#endif
        }



        /// <summary>
        /// 从选择集中提取实体对象（保留用于向后兼容）
        /// </summary>
        /// <param name="selectionSet">选择集</param>
        /// <param name="transaction">事务对象</param>
        /// <returns>实体对象列表</returns>
        public static List<Entity> GetEntitiesFromSelection(SelectionSet selectionSet, Transaction transaction)
        {
            var entities = new List<Entity>();

            if (selectionSet == null || transaction == null)
                return entities;

            try
            {
                foreach (SelectedObject selectedObj in selectionSet)
                {
                    if (selectedObj != null)
                    {
                        var entity = transaction.GetObject(selectedObj.ObjectId, OpenMode.ForRead) as Entity;
                        if (entity != null)
                        {
                            entities.Add(entity);
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.DatabaseOperationFailed,
                    $"从选择集提取实体时失败: {ex.Message}", ex);
            }

            return entities;
        }

        /// <summary>
        /// 验证选择的实体是否有效
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <param name="editor">编辑器对象</param>
        /// <returns>是否有效</returns>
        public static bool ValidateSelectedEntities(List<Entity> entities, Editor editor)
        {
            if (entities == null || entities.Count == 0)
            {
                ShowError(editor, "未选择任何有效的图形实体");
                return false;
            }

            ShowDebugInfo(editor, $"选择了 {entities.Count} 个图形实体");
            return true;
        }
    }
}
