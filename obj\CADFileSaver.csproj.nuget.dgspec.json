{"format": 1, "restore": {"E:\\AI程序\\2025年6月\\CAD\\文件按要求保存\\2.0 - 低版本 - 重做窗体\\2.0 - 低版本 - 重做窗体\\CADFileSaver.csproj": {}}, "projects": {"E:\\AI程序\\2025年6月\\CAD\\文件按要求保存\\2.0 - 低版本 - 重做窗体\\2.0 - 低版本 - 重做窗体\\CADFileSaver.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\AI程序\\2025年6月\\CAD\\文件按要求保存\\2.0 - 低版本 - 重做窗体\\2.0 - 低版本 - 重做窗体\\CADFileSaver.csproj", "projectName": "CADFileSaver", "projectPath": "E:\\AI程序\\2025年6月\\CAD\\文件按要求保存\\2.0 - 低版本 - 重做窗体\\2.0 - 低版本 - 重做窗体\\CADFileSaver.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\AI程序\\2025年6月\\CAD\\文件按要求保存\\2.0 - 低版本 - 重做窗体\\2.0 - 低版本 - 重做窗体\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"AutoCAD.NET": {"target": "Package", "version": "[24.2.0, )"}, "AutoCAD.NET.Core": {"target": "Package", "version": "[24.2.0, )"}, "AutoCAD.NET.Model": {"target": "Package", "version": "[24.2.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}