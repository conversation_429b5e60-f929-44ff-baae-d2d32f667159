using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using System.IO;
using CADFileSaver.Core;
using CADFileSaver.Features.ColorSeparation;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using AcadApp = Autodesk.AutoCAD.ApplicationServices.Application;

namespace CADFileSaver.Forms
{
    /// <summary>
    /// 主窗体
    /// </summary>
    public partial class MainForm : Form
    {
        private ConfigManager.FormConfig _config;
        private bool _isProcessing = false;
        // 移除未使用的材料变更标志，因为不再作为退出条件
        // private bool _materialChanged = false;
        private bool _isInContinuousMode = false; // 连续导出模式标志

        public MainForm()
        {
            InitializeComponent();
            LoadConfiguration();
            InitializeControls();
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            _config = ConfigManager.LoadConfig();
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            // 初始化Box5 - 导出文件的控件（现有功能）
            InitializeExportControls();

            // 初始化新增功能的控件
            InitializeNewFeatureControls();
        }

        /// <summary>
        /// 初始化导出文件相关控件（现有功能）
        /// </summary>
        private void InitializeExportControls()
        {
            // 初始化材料下拉框
            cmbMaterial.Items.Clear();
            cmbMaterial.Items.AddRange(_config.Materials.ToArray());
            if (cmbMaterial.Items.Contains(_config.MaterialName))
                cmbMaterial.SelectedItem = _config.MaterialName;
            else if (cmbMaterial.Items.Count > 0)
                cmbMaterial.SelectedIndex = 0;

            // 初始化边框颜色下拉框
            cmbBorderColor.Items.Clear();
            cmbBorderColor.Items.AddRange(GeometryHelper.GetSupportedColors());
            if (cmbBorderColor.Items.Contains(_config.BorderColor))
                cmbBorderColor.SelectedItem = _config.BorderColor;
            else
                cmbBorderColor.SelectedIndex = 0; // 默认选择第一个

            // 初始化文件类型下拉框
            cmbFileType.Items.Clear();
            cmbFileType.Items.AddRange(FileHelper.GetSupportedFileFormats());
            if (cmbFileType.Items.Contains(_config.FileType))
                cmbFileType.SelectedItem = _config.FileType;
            else if (cmbFileType.Items.Count > 0)
                cmbFileType.SelectedIndex = 0;

            // 设置其他控件的值
            txtOrderNumber.Text = _config.OrderNumber;
            txtRemark.Text = _config.Remark;
            txtSerial.Text = _config.SerialNumber;
            txtDimension.Text = _config.Dimension;
            txtSavePath.Text = _config.SavePath;

            // 设置材料下拉框的最佳显示高度
            SetOptimalDropDownHeight();
        }

        /// <summary>
        /// 初始化新功能控件
        /// </summary>
        private void InitializeNewFeatureControls()
        {
            // 初始化Box1 - 标号功能
            InitializeNumberingControls();

            // 初始化Box2 - 成块功能
            InitializeBlockControls();

            // 初始化Box3 - 摆料功能
            InitializeLayoutControls();

            // 初始化Box4 - 分色功能
            InitializeColorControls();

            // 初始化ToolTip功能
            InitializeToolTips();
        }

        /// <summary>
        /// 初始化标号功能控件
        /// </summary>
        private void InitializeNumberingControls()
        {
            // 设置数字输入验证
            txtNumberingSuffix.KeyPress += NumericTextBox_KeyPress;
            txtHorizontalOffset.KeyPress += NumericTextBox_KeyPress;
            txtVerticalOffset.KeyPress += NumericTextBox_KeyPress;
        }

        /// <summary>
        /// 初始化成块功能控件
        /// </summary>
        private void InitializeBlockControls()
        {
            // 设置数字输入验证
            txtBlockDistance.KeyPress += NumericTextBox_KeyPress;
        }

        /// <summary>
        /// 初始化摆料功能控件
        /// </summary>
        private void InitializeLayoutControls()
        {
            // 摆料功能暂无需要特殊初始化的控件
        }

        /// <summary>
        /// 初始化分色功能控件
        /// </summary>
        private void InitializeColorControls()
        {
            // 设置外框颜色下拉框默认值
            if (cmbFrameColor.Items.Count > 0)
                cmbFrameColor.SelectedIndex = 4; // 默认选择"青色"
        }

        /// <summary>
        /// 初始化ToolTip功能
        /// </summary>
        private void InitializeToolTips()
        {
            // 为编号功能下拉框设置ToolTip
            toolTipDescription.SetToolTip(cmbNumberingDescription,
                "编号功能工作流程：请在第一个文本框填入名字的缩写，第二个文本框默认是01，也可以手动填入想要的数字。点击开始编号后，鼠标点击任意位置即可在对应位置生成需要的编号。");

            // 为成块功能下拉框设置ToolTip
            toolTipDescription.SetToolTip(cmbBlockDescription,
                "成块功能工作流程：包围盒的距离是划蓝线和材料之间的间距，可以根据需求设置，设置完成后点击单次成块则只对一组图形生成外边框并成块，点击批量成块则可以连续对不同图形进行操作，直到按下ESC退出为止。");

            // 为摆料功能下拉框设置ToolTip
            toolTipDescription.SetToolTip(cmbLayoutDescription,
                "摆料功能工作流程：首先需要使用rec命令画一个符合要求的版料矩形(例如980*980)，然后再点击[横向摆料]或者[竖向摆料]，然后选取需要摆料的所有图形，按下空格，然后点击刚才画好的板料矩形，按下空格即可");

            // 为分色功能下拉框设置ToolTip
            toolTipDescription.SetToolTip(cmbColorDescription,
                "分色功能工作流程：在开始分色直线需要把摆好的料彻底炸碎，保险起见建议多炸几次。然后点击[竖向分色]或者[横向分色]，分色方向应该与摆料方向相反，如果你是横向摆料那么就选纵向分色，如果你是纵向摆料那么就选横向分色，点击完成后你可以选择需要分色的所有图形，按下空格等待运算完毕，运算完后把多余的外框手动删除即可");
        }

        /// <summary>
        /// 数字输入验证
        /// </summary>
        private void NumericTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            // 只允许数字、退格键和小数点
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && e.KeyChar != '.')
            {
                e.Handled = true;
            }

            // 只允许一个小数点
            if (e.KeyChar == '.' && ((TextBox)sender).Text.IndexOf('.') > -1)
            {
                e.Handled = true;
            }
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void MainForm_Load(object sender, EventArgs e)
        {
            // 确保窗体置顶
            this.TopMost = true;
            this.BringToFront();

            // 设置材料下拉框的最佳显示高度
            SetOptimalDropDownHeight();
        }

        /// <summary>
        /// 材料选择改变事件
        /// </summary>
        private void cmbMaterial_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 材料改变时重置序号为01
            txtSerial.Text = "01";

            // 如果在连续导出模式中，给用户友好提示，但不退出循环
            if (_isInContinuousMode && cmbMaterial.SelectedItem != null)
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                doc?.Editor.WriteMessage($"\n材料已更换为: {cmbMaterial.SelectedItem}，序号重置为01\n");
            }
        }

        /// <summary>
        /// 管理材料按钮点击事件
        /// </summary>
        private void btnManageMaterial_Click(object sender, EventArgs e)
        {
            // 临时禁用主窗体置顶，确保材料管理窗体能正常显示在前面
            bool wasTopMost = this.TopMost;
            this.TopMost = false;

            try
            {
                using (var materialForm = new MaterialManagerForm(_config.Materials))
                {
                    // 设置子窗体的父窗体关系和置顶属性
                    materialForm.Owner = this;
                    materialForm.TopMost = true;

                    if (materialForm.ShowDialog() == DialogResult.OK)
                    {
                        // 更新材料列表
                        _config.Materials = materialForm.Materials;

                        // 重新初始化材料下拉框
                        var selectedMaterial = cmbMaterial.SelectedItem?.ToString();
                        cmbMaterial.Items.Clear();
                        cmbMaterial.Items.AddRange(_config.Materials.ToArray());

                        // 重新设置最佳显示高度（材料数量可能已变化）
                        SetOptimalDropDownHeight();

                        // 尝试保持之前的选择
                        if (!string.IsNullOrEmpty(selectedMaterial) && cmbMaterial.Items.Contains(selectedMaterial))
                            cmbMaterial.SelectedItem = selectedMaterial;
                        else if (cmbMaterial.Items.Count > 0)
                            cmbMaterial.SelectedIndex = 0;

                        // 保存配置
                        SaveConfiguration();
                    }
                }
            }
            finally
            {
                // 恢复主窗体的置顶状态，确保不影响其他功能
                this.TopMost = wasTopMost;
                this.BringToFront();
            }
        }

        /// <summary>
        /// 浏览路径按钮点击事件
        /// </summary>
        private void btnBrowsePath_Click(object sender, EventArgs e)
        {
            using (var folderDialog = new FolderBrowserDialog())
            {
                folderDialog.Description = "选择文件保存路径";
                folderDialog.SelectedPath = txtSavePath.Text;
                
                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    txtSavePath.Text = folderDialog.SelectedPath;
                }
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void btnOK_Click(object sender, EventArgs e)
        {
            if (_isProcessing)
            {
                MessageBox.Show("正在处理中，请稍候...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 验证输入
            if (!ValidateInput())
                return;

            // 保存当前配置
            SaveConfiguration();

            // 开始图形选择和处理流程
            ProcessGraphicsSelection();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            if (_isProcessing)
            {
                MessageBox.Show("正在处理中，无法取消", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            this.Close();
        }

        /// <summary>
        /// 验证输入
        /// </summary>
        private bool ValidateInput()
        {
            // 检查必填项
            if (cmbMaterial.SelectedItem == null)
            {
                MessageBox.Show("请选择材料名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbMaterial.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtSerial.Text))
            {
                MessageBox.Show("请输入序号", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSerial.Focus();
                return false;
            }

            if (cmbBorderColor.SelectedItem == null)
            {
                MessageBox.Show("请选择边框颜色", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbBorderColor.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtSavePath.Text))
            {
                MessageBox.Show("请选择文件保存路径", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSavePath.Focus();
                return false;
            }

            if (cmbFileType.SelectedItem == null)
            {
                MessageBox.Show("请选择文件类型", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbFileType.Focus();
                return false;
            }

            // 验证路径
            if (!FileHelper.IsValidPath(txtSavePath.Text, out string pathError))
            {
                MessageBox.Show(pathError, "路径验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSavePath.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        private void SaveConfiguration()
        {
            _config.OrderNumber = txtOrderNumber.Text.Trim();
            _config.MaterialName = cmbMaterial.SelectedItem?.ToString() ?? "";
            _config.Remark = txtRemark.Text.Trim();
            _config.SerialNumber = txtSerial.Text.Trim();
            _config.Dimension = txtDimension.Text.Trim();
            _config.BorderColor = cmbBorderColor.SelectedItem?.ToString() ?? "";
            _config.SavePath = txtSavePath.Text.Trim();
            _config.FileType = cmbFileType.SelectedItem?.ToString() ?? "";

            ConfigManager.SaveConfig(_config);
        }

        /// <summary>
        /// 设置材料下拉框的最佳显示高度
        /// 根据材料数量和屏幕空间动态调整，确保良好的用户体验
        /// </summary>
        private void SetOptimalDropDownHeight()
        {
            try
            {
                // 计算单个项目的高度
                int itemHeight = cmbMaterial.ItemHeight;

                // 根据材料数量设置最佳显示项目数
                int itemCount = cmbMaterial.Items.Count;
                int optimalVisibleItems;

                if (itemCount <= 5)
                    optimalVisibleItems = itemCount; // 少于5项时全部显示
                else if (itemCount <= 15)
                    optimalVisibleItems = 8; // 中等数量显示8项
                else if (itemCount <= 30)
                    optimalVisibleItems = 10; // 较多数量显示10项
                else
                    optimalVisibleItems = 12; // 大量材料显示12项

                // 计算理想高度
                int idealHeight = itemHeight * optimalVisibleItems + 4; // +4为边框和间距

                // 获取可用屏幕空间（考虑窗体位置）
                var screen = Screen.FromControl(this);
                int availableHeight = screen.WorkingArea.Height - this.Bottom - 50; // 预留50像素

                // 设置最终高度（确保不超出屏幕且至少显示3项）
                int minHeight = itemHeight * 3 + 4; // 最少显示3项
                int finalHeight = Math.Min(idealHeight, Math.Max(availableHeight, minHeight));

                // 应用设置
                cmbMaterial.DropDownHeight = finalHeight;
                cmbMaterial.MaxDropDownItems = optimalVisibleItems;
            }
            catch (System.Exception ex)
            {
                // 如果计算失败，使用安全的默认值
                cmbMaterial.DropDownHeight = 200;
                cmbMaterial.MaxDropDownItems = 8;

                // 可选：记录错误（在实际部署中可以添加日志）
                System.Diagnostics.Debug.WriteLine($"设置下拉框高度时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理图形选择 - 连续导出模式
        /// </summary>
        private void ProcessGraphicsSelection()
        {
            try
            {
                _isProcessing = true;
                _isInContinuousMode = true;
                // 移除材料变更标志重置，因为不再作为退出条件
                // _materialChanged = false;

                // 保持窗体显示，不再隐藏
                // this.WindowState = FormWindowState.Minimized; // 已移除

                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                var editor = doc.Editor;

                editor.WriteMessage("\n=== 开始连续导出模式 ===\n");
                editor.WriteMessage("提示：按ESC退出连续导出，更换材料不影响循环继续\n");

                int exportCount = 0; // 导出计数器

                while (true)
                {
                    // 移除材料变更检查，保持循环连续性
                    // 用户更换材料不再退出循环，只有按ESC键才退出

                    // 提示用户选择图形
                    editor.WriteMessage($"\n[第{exportCount + 1}次导出] 请选择要保存的图形，完成后按空格键...\n");

                    var selectionOptions = new PromptSelectionOptions
                    {
                        MessageForAdding = "选择图形: ",
                        MessageForRemoval = "移除图形: "
                    };

                    var selectionResult = editor.GetSelection(selectionOptions);

                    // 检查退出条件
                    if (selectionResult.Status == PromptStatus.Cancel)
                    {
                        editor.WriteMessage("\n用户按ESC取消，退出连续导出模式\n");
                        break;
                    }

                    if (selectionResult.Status != PromptStatus.OK)
                    {
                        editor.WriteMessage("\n未选择任何图形，退出连续导出模式\n");
                        break;
                    }

                    // 处理选择的图形
                    if (ProcessSelectedEntities(selectionResult.Value))
                    {
                        exportCount++;
                        editor.WriteMessage($"已完成 {exportCount} 个文件的导出\n");
                        // 成功后继续下一轮
                        continue;
                    }
                    else
                    {
                        // 失败时退出
                        editor.WriteMessage("\n处理失败，退出连续导出模式\n");
                        break;
                    }
                }

                editor.WriteMessage($"\n=== 连续导出模式结束，共导出 {exportCount} 个文件 ===\n");
            }
            catch (System.Exception ex)
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                doc?.Editor.WriteMessage($"\n处理过程中发生错误: {ex.Message}\n");
            }
            finally
            {
                _isProcessing = false;
                _isInContinuousMode = false;
                // 清理状态，简化状态管理
            }
        }

        /// <summary>
        /// 处理选择的实体
        /// </summary>
        /// <param name="selectionSet">选择集</param>
        /// <returns>处理是否成功</returns>
        private bool ProcessSelectedEntities(SelectionSet selectionSet)
        {
            try
            {
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                var db = doc.Database;

                using (var transaction = db.TransactionManager.StartTransaction())
                {
                    var entities = new List<Entity>();

                    // 获取所有选择的实体
                    foreach (SelectedObject selectedObj in selectionSet)
                    {
                        if (selectedObj != null)
                        {
                            var entity = transaction.GetObject(selectedObj.ObjectId, OpenMode.ForRead) as Entity;
                            if (entity != null)
                                entities.Add(entity);
                        }
                    }

                    if (!entities.Any())
                    {
                        doc.Editor.WriteMessage("\n没有找到有效的图形实体\n");
                        return false;
                    }

                    // 计算尺寸
                    var borderColor = cmbBorderColor.SelectedItem.ToString();
                    var (maxLength, minLength) = GeometryHelper.CalculateMinMaxLength(entities, borderColor);

                    if (maxLength <= 0 || minLength <= 0)
                    {
                        doc.Editor.WriteMessage($"\n未找到符合颜色条件({borderColor})的图形，无法计算尺寸\n");
                        return false;
                    }

                    // 更新尺寸显示
                    var dimension = GeometryHelper.FormatDimension(maxLength, minLength);
                    this.Invoke(new Action(() => {
                        txtDimension.Text = dimension;
                        _config.Dimension = dimension;
                    }));

                    // 生成文件名
                    var fileName = FileHelper.GenerateFileName(
                        txtOrderNumber.Text.Trim(),
                        cmbMaterial.SelectedItem.ToString(),
                        txtRemark.Text.Trim(),
                        txtSerial.Text.Trim(),
                        dimension
                    );

                    fileName = FileHelper.CleanFileName(fileName);
                    var extension = FileHelper.GetFileExtension(cmbFileType.SelectedItem.ToString());
                    var fullPath = Path.Combine(txtSavePath.Text.Trim(), fileName + extension);

                    // 保存文件
                    var entityIds = new ObjectIdCollection(selectionSet.GetObjectIds());
                    var success = FileHelper.SaveEntitiesToFile(entityIds, fullPath,
                        cmbFileType.SelectedItem.ToString(), out string errorMessage);

                    transaction.Commit();

                    if (success)
                    {
                        // 更新序号
                        var nextSerial = ConfigManager.GetNextSerialNumber(txtSerial.Text.Trim());
                        this.Invoke(new Action(() => {
                            txtSerial.Text = nextSerial;
                            _config.SerialNumber = nextSerial;
                        }));

                        // 保存配置
                        SaveConfiguration();

                        // 使用命令行提示替代MessageBox
                        doc.Editor.WriteMessage($"\n文件已成功导出到指定文件夹: {Path.GetDirectoryName(fullPath)}\n");
                        doc.Editor.WriteMessage($"文件名: {Path.GetFileName(fullPath)}\n");

                        return true; // 返回成功，继续循环
                    }
                    else
                    {
                        doc.Editor.WriteMessage($"\n保存失败: {errorMessage}\n");
                        return false; // 返回失败，退出循环
                    }
                }
            }
            catch (System.Exception ex)
            {
                var docEx = AcadApp.DocumentManager.MdiActiveDocument;
                docEx?.Editor.WriteMessage($"\n处理实体时发生错误: {ex.Message}\n");
                return false;
            }
        }

        /// <summary>
        /// 恢复窗体显示
        /// </summary>
        private void RestoreWindow()
        {
            this.Invoke(new Action(() => {
                this.WindowState = FormWindowState.Normal;
                this.BringToFront();
                this.TopMost = true;
            }));
        }

        #region 新功能事件处理

        // Box1 - 标号功能事件处理
        private void btnStartNumbering_Click(object sender, EventArgs e)
        {
            // TODO: 实现标号功能
            MessageBox.Show("标号功能将在后续版本中实现", "功能提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnCancelNumbering_Click(object sender, EventArgs e)
        {
            // 重置标号相关控件
            txtNumberingPrefix.Text = "";
            txtNumberingSuffix.Text = "01";
            txtHorizontalOffset.Text = "0";
            txtVerticalOffset.Text = "0";
        }

        // Box2 - 成块功能事件处理
        private void btnSingleBlock_Click(object sender, EventArgs e)
        {
            // TODO: 实现单次成块功能
            MessageBox.Show("单次成块功能将在后续版本中实现", "功能提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnBatchBlock_Click(object sender, EventArgs e)
        {
            // TODO: 实现批量成块功能
            MessageBox.Show("批量成块功能将在后续版本中实现", "功能提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnCancelBlock_Click(object sender, EventArgs e)
        {
            // 重置成块相关控件
            txtBlockDistance.Text = "6";
        }

        // Box3 - 摆料功能事件处理
        private void btnHorizontalLayout_Click(object sender, EventArgs e)
        {
            // TODO: 实现横向摆料功能
            MessageBox.Show("横向摆料功能将在后续版本中实现", "功能提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnVerticalLayout_Click(object sender, EventArgs e)
        {
            // TODO: 实现竖向摆料功能
            MessageBox.Show("竖向摆料功能将在后续版本中实现", "功能提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnCancelLayout_Click(object sender, EventArgs e)
        {
            // 摆料功能没有需要重置的控件
        }

        // Box4 - 分色功能事件处理
        private void btnVerticalColor_Click(object sender, EventArgs e)
        {
            // 使用安全执行包装器，确保异常不影响其他功能
            SafeExecutionWrapper.ExecuteColorSeparation(() =>
            {
                // 创建分色功能管理器
                var colorSeparationManager = new ColorSeparationManager(
                    cmbFrameColor,  // 外框颜色下拉框
                    chkRed,         // 红色复选框
                    chkBlue,        // 蓝色复选框
                    chkGreen,       // 绿色复选框
                    chkYellow,      // 黄色复选框
                    chkCyan,        // 青色复选框
                    chkMagenta      // 洋红复选框
                );

                // 执行竖向分色操作
                var result = colorSeparationManager.ExecuteVerticalColorSeparation();

                // 显示操作结果摘要
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                if (doc != null && result.Success)
                {
                    doc.Editor.WriteMessage($"\n✓ {result.Message}\n");
                }
            });
        }

        private void btnHorizontalColor_Click(object sender, EventArgs e)
        {
            // 使用安全执行包装器，确保异常不影响其他功能
            SafeExecutionWrapper.ExecuteColorSeparation(() =>
            {
                // 第一阶段：提取配置信息
                var colorSeparationManager = new ColorSeparationManager(
                    cmbFrameColor,  // 外框颜色下拉框
                    chkRed,         // 红色复选框
                    chkBlue,        // 蓝色复选框
                    chkGreen,       // 绿色复选框
                    chkYellow,      // 黄色复选框
                    chkCyan,        // 青色复选框
                    chkMagenta      // 洋红复选框
                );

                // 提取UI配置
                var config = ExtractHorizontalColorConfig();
                if (!config.IsValid(out string validationMessage))
                {
                    throw new ColorSeparationException(
                        ColorSeparationException.ErrorType.InvalidColorSelection,
                        validationMessage);
                }

                // 第二阶段：用户交互
                var doc = AcadApp.DocumentManager.MdiActiveDocument;
                var editor = doc.Editor;

                // 显示操作开始信息
                editor.WriteMessage("\n=== 开始横向分色操作 ===\n");
                editor.WriteMessage($"外框颜色: {config.FrameColor}\n");
                editor.WriteMessage($"分色项目: [{string.Join(", ", config.SeparationColors)}]\n");
                editor.WriteMessage($"将生成 {config.SeparationColors.Count} 个颜色组合\n");

                // 获取用户选择的图形
                var selectionSet = UserInteractionHelper.GetUserSelection(editor, out var status);

                if (status == PromptStatus.Cancel)
                {
                    throw new ColorSeparationException(
                        ColorSeparationException.ErrorType.UserCancelled,
                        "用户取消操作");
                }

                if (status != PromptStatus.OK || selectionSet == null)
                {
                    throw new ColorSeparationException(
                        ColorSeparationException.ErrorType.NoEntitiesSelected,
                        "未选择任何图形实体");
                }

                // 第三阶段：执行横向分色处理
                var processor = new HorizontalColorSeparationProcessor(config);
                var result = processor.ExecuteHorizontalColorSeparation(selectionSet);

                // 显示操作结果摘要
                if (result.Success)
                {
                    editor.WriteMessage($"\n✓ {result.Message}\n");
                    editor.WriteMessage("=== 横向分色操作完成 ===\n");
                }
            });
        }

        private void btnCancelColor_Click(object sender, EventArgs e)
        {
            // 重置分色相关控件
            if (cmbFrameColor.Items.Count > 0)
                cmbFrameColor.SelectedIndex = 4; // 重置为"青色"

            // 重置复选框
            chkRed.Checked = true;
            chkBlue.Checked = true;
            chkGreen.Checked = true;
            chkYellow.Checked = true;
            chkCyan.Checked = false;
            chkMagenta.Checked = false;
        }

        /// <summary>
        /// 提取横向分色配置信息
        /// </summary>
        /// <returns>分色配置对象</returns>
        private ColorSeparationConfig ExtractHorizontalColorConfig()
        {
            var config = new ColorSeparationConfig();

            try
            {
                // 定义有效的颜色名称
                var validColors = new[] { "红色", "蓝色", "绿色", "黄色", "青色", "洋红" };

                // 提取外框颜色
                var selectedFrameColor = cmbFrameColor.SelectedItem?.ToString();

                if (!string.IsNullOrEmpty(selectedFrameColor) && validColors.Contains(selectedFrameColor))
                {
                    config.FrameColor = selectedFrameColor;
                }
                else
                {
                    // 如果选择的不是有效颜色，使用默认值
                    config.FrameColor = "青色";

                    // 记录警告信息
                    var doc = AcadApp.DocumentManager.MdiActiveDocument;
                    doc?.Editor.WriteMessage($"\n⚠ 警告: 外框颜色选择无效('{selectedFrameColor}')，使用默认颜色'青色'\n");
                }

                // 按固定顺序提取选中的分色项目
                var colorNames = new[] { "红色", "蓝色", "绿色", "黄色", "青色", "洋红" };
                var colorCheckBoxes = new[] { chkRed, chkBlue, chkGreen, chkYellow, chkCyan, chkMagenta };

                for (int i = 0; i < colorCheckBoxes.Length && i < colorNames.Length; i++)
                {
                    if (colorCheckBoxes[i].Checked)
                    {
                        config.SeparationColors.Add(colorNames[i]);
                    }
                }

                return config;
            }
            catch (System.Exception ex)
            {
                throw new ColorSeparationException(
                    ColorSeparationException.ErrorType.InvalidColorSelection,
                    $"提取横向分色配置时发生错误: {ex.Message}", ex);
            }
        }

        #endregion
    }
}
